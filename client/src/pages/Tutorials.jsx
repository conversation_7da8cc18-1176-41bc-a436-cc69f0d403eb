import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Play,
  Clock,
  Users,
  Star,
  Search,
  Filter,
  BookOpen,
  Video,
  Download,
  Share2,
  Eye,
  ThumbsUp,
} from "lucide-react";
import { toast } from "sonner";

const Tutorials = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");

  const tutorialCategories = [
    { id: "all", name: "All Tutorials", count: 24 },
    { id: "getting-started", name: "Getting Started", count: 6 },
    { id: "products", name: "Product Management", count: 5 },
    { id: "ecommerce", name: "E-Commerce", count: 4 },
    { id: "analytics", name: "Analytics & Reports", count: 3 },
    { id: "integrations", name: "Integrations", count: 4 },
    { id: "advanced", name: "Advanced Features", count: 2 },
  ];

  const tutorials = [
    {
      id: 1,
      title: "Getting Started with OmniBiz",
      description: "Learn the basics of setting up your OmniBiz account and dashboard",
      category: "getting-started",
      duration: "8:45",
      views: 12500,
      likes: 890,
      difficulty: "Beginner",
      thumbnail: "/api/placeholder/320/180",
      videoUrl: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
      topics: ["Account Setup", "Dashboard Overview", "Basic Navigation"]
    },
    {
      id: 2,
      title: "Product Catalog Management",
      description: "Complete guide to adding, organizing, and managing your product inventory",
      category: "products",
      duration: "12:30",
      views: 8900,
      likes: 654,
      difficulty: "Beginner",
      thumbnail: "/api/placeholder/320/180",
      videoUrl: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
      topics: ["Adding Products", "Categories", "Inventory Tracking", "Bulk Import"]
    },
    {
      id: 3,
      title: "Setting Up Your Online Store",
      description: "Step-by-step guide to launching your e-commerce store with OmniBiz",
      category: "ecommerce",
      duration: "15:20",
      views: 15600,
      likes: 1200,
      difficulty: "Intermediate",
      thumbnail: "/api/placeholder/320/180",
      videoUrl: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
      topics: ["Store Configuration", "Payment Setup", "Shipping Options", "Customer Management"]
    },
    {
      id: 4,
      title: "Advanced Analytics and Reporting",
      description: "Unlock powerful insights with OmniBiz analytics and custom reports",
      category: "analytics",
      duration: "18:45",
      views: 6700,
      likes: 445,
      difficulty: "Advanced",
      thumbnail: "/api/placeholder/320/180",
      videoUrl: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
      topics: ["Custom Reports", "Data Export", "Performance Metrics", "Forecasting"]
    },
    {
      id: 5,
      title: "API Integration Basics",
      description: "Learn how to integrate OmniBiz with your existing systems using our API",
      category: "integrations",
      duration: "22:15",
      views: 4200,
      likes: 312,
      difficulty: "Advanced",
      thumbnail: "/api/placeholder/320/180",
      videoUrl: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
      topics: ["API Authentication", "Webhooks", "Data Sync", "Error Handling"]
    },
    {
      id: 6,
      title: "Mobile App Management",
      description: "Manage your business on the go with the OmniBiz mobile app",
      category: "getting-started",
      duration: "10:30",
      views: 9800,
      likes: 720,
      difficulty: "Beginner",
      thumbnail: "/api/placeholder/320/180",
      videoUrl: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
      topics: ["Mobile Setup", "Offline Mode", "Push Notifications", "Quick Actions"]
    }
  ];

  const filteredTutorials = tutorials.filter(tutorial => {
    const matchesSearch = tutorial.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tutorial.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tutorial.topics.some(topic => topic.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesCategory = selectedCategory === "all" || tutorial.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case "Beginner": return "bg-green-100 text-green-800";
      case "Intermediate": return "bg-yellow-100 text-yellow-800";
      case "Advanced": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const handlePlayVideo = (tutorial) => {
    window.open(tutorial.videoUrl, '_blank');
    toast.success(`Opening: ${tutorial.title}`);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            OmniBiz Video Tutorials
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Learn how to make the most of OmniBiz with our comprehensive video tutorials. 
            From basic setup to advanced features, we've got you covered.
          </p>
        </div>

        {/* Search and Filter */}
        <div className="mb-8">
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search tutorials..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2 flex-wrap">
              {tutorialCategories.map(category => (
                <Button
                  key={category.id}
                  variant={selectedCategory === category.id ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(category.id)}
                  className="text-xs"
                >
                  {category.name} ({category.count})
                </Button>
              ))}
            </div>
          </div>
        </div>

        {/* Tutorial Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTutorials.map(tutorial => (
            <Card key={tutorial.id} className="hover:shadow-lg transition-shadow">
              <div className="relative">
                <img
                  src={tutorial.thumbnail}
                  alt={tutorial.title}
                  className="w-full h-48 object-cover rounded-t-lg"
                />
                <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity rounded-t-lg">
                  <Button
                    size="lg"
                    onClick={() => handlePlayVideo(tutorial)}
                    className="bg-white text-black hover:bg-gray-100"
                  >
                    <Play className="h-6 w-6 mr-2" />
                    Play Video
                  </Button>
                </div>
                <Badge 
                  className={`absolute top-2 right-2 ${getDifficultyColor(tutorial.difficulty)}`}
                >
                  {tutorial.difficulty}
                </Badge>
              </div>
              
              <CardHeader>
                <CardTitle className="text-lg">{tutorial.title}</CardTitle>
                <CardDescription>{tutorial.description}</CardDescription>
              </CardHeader>
              
              <CardContent>
                <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                  <div className="flex items-center gap-4">
                    <span className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      {tutorial.duration}
                    </span>
                    <span className="flex items-center gap-1">
                      <Eye className="h-4 w-4" />
                      {tutorial.views.toLocaleString()}
                    </span>
                    <span className="flex items-center gap-1">
                      <ThumbsUp className="h-4 w-4" />
                      {tutorial.likes}
                    </span>
                  </div>
                </div>
                
                <div className="flex flex-wrap gap-1 mb-4">
                  {tutorial.topics.slice(0, 3).map((topic, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {topic}
                    </Badge>
                  ))}
                  {tutorial.topics.length > 3 && (
                    <Badge variant="secondary" className="text-xs">
                      +{tutorial.topics.length - 3} more
                    </Badge>
                  )}
                </div>
                
                <div className="flex gap-2">
                  <Button 
                    className="flex-1" 
                    onClick={() => handlePlayVideo(tutorial)}
                  >
                    <Play className="h-4 w-4 mr-2" />
                    Watch Now
                  </Button>
                  <Button variant="outline" size="icon">
                    <Share2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredTutorials.length === 0 && (
          <div className="text-center py-12">
            <Video className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No tutorials found</h3>
            <p className="text-gray-500">Try adjusting your search or filter criteria.</p>
          </div>
        )}

        {/* Call to Action */}
        <div className="mt-12 text-center">
          <Card className="max-w-2xl mx-auto">
            <CardContent className="p-8">
              <BookOpen className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <h3 className="text-2xl font-bold mb-4">Need More Help?</h3>
              <p className="text-gray-600 mb-6">
                Can't find what you're looking for? Our support team is here to help you succeed.
              </p>
              <div className="flex gap-4 justify-center">
                <Button onClick={() => window.location.href = '/dashboard/support'}>
                  Contact Support
                </Button>
                <Button variant="outline" onClick={() => window.open('https://docs.omnibiz.com', '_blank')}>
                  Read Documentation
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Tutorials;
