import { useState, useEffect, use<PERSON><PERSON>back, use<PERSON>emo } from "react";
import { toast } from "sonner";
import api from "@/lib/api";
import { useAuth } from "@/context/AuthContext";
import { useFinancial } from "@/context/FinancialContext";
import { useSocket } from "@/context/SocketContext";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  ShoppingCart as ShoppingCartIcon,
  Package,
  CreditCard,
  TrendingUp,
  Eye,
  Plus,
  Link2,
  Copy,
  Share2,
  Send,
  Store,
  Users,
  BarChart3,
  Settings,
  RefreshCw,
  Loader2,
  Activity,
  Clock,
  CheckCircle,
  AlertCircle,
  DollarSign,
  ShoppingBag,
  Zap
} from "lucide-react";
import ShoppingCart from "@/components/ShoppingCart";
import Checkout from "@/components/Checkout";
import OrderManagement from "@/components/OrderManagement";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const defaultProduct = {
  name: "",
  sku: "",
  category: "",
  stockQuantity: "",
  reorderLevel: "",
  price: "",
  supplierName: "",
  description: "",
};

const ECommerce = () => {
  const { isAuthenticated, user } = useAuth();
  const { refreshFinancialData } = useFinancial();
  const { socket, connected, emit } = useSocket();

  // Core state
  const [orders, setOrders] = useState([]);
  const [products, setProducts] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Dialog states
  const [isEditOrderOpen, setIsEditOrderOpen] = useState(false);
  const [editOrder, setEditOrder] = useState(null);
  const [isViewOrderOpen, setIsViewOrderOpen] = useState(false);
  const [viewOrder, setViewOrder] = useState(null);
  const [isAddProductOpen, setIsAddProductOpen] = useState(false);
  const [newProduct, setNewProduct] = useState(defaultProduct);

  // Invite Link State
  const [inviteLink, setInviteLink] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);

  // E-commerce State
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [showCheckout, setShowCheckout] = useState(false);
  const [checkoutData, setCheckoutData] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');

  // Real-time metrics state
  const [liveMetrics, setLiveMetrics] = useState({
    totalOrders: 0,
    totalRevenue: 0,
    activeCustomers: 0,
    pendingOrders: 0,
    completedOrders: 0,
    cancelledOrders: 0,
    averageOrderValue: 0,
    conversionRate: 0
  });

  // Live activity feed
  const [activityFeed, setActivityFeed] = useState([]);

  // Real-time data fetching functions
  const fetchAllData = useCallback(async () => {
    if (!isAuthenticated) {
      toast.error("Please log in to view your data.");
      return;
    }

    try {
      setLoading(true);

      // Fetch all data in parallel
      const [ordersRes, productsRes, metricsRes] = await Promise.all([
        api.get("/orders"),
        api.get("/products"),
        api.get("/dashboard/analytics/ecommerce")
      ]);

      setOrders(ordersRes.data || []);
      setProducts(productsRes.data || []);

      // Update live metrics
      const metrics = metricsRes.data || {};
      setLiveMetrics({
        totalOrders: metrics.totalOrders || 0,
        totalRevenue: metrics.totalRevenue || 0,
        activeCustomers: metrics.activeCustomers || 0,
        pendingOrders: metrics.pendingOrders || 0,
        completedOrders: metrics.completedOrders || 0,
        cancelledOrders: metrics.cancelledOrders || 0,
        averageOrderValue: metrics.averageOrderValue || 0,
        conversionRate: metrics.conversionRate || 0
      });

    } catch (error) {
      toast.error("Failed to load e-commerce data");
      console.error("Error fetching data:", error);
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated]);

  const refreshData = useCallback(async () => {
    setRefreshing(true);
    await fetchAllData();
    setRefreshing(false);
    toast.success('Data refreshed successfully!');
  }, [fetchAllData]);

  // Initial data fetch
  useEffect(() => {
    fetchAllData();
  }, [fetchAllData]);

  // Real-time Socket.IO event listeners
  useEffect(() => {
    if (!socket || !connected) return;

    // Join e-commerce room for real-time updates
    socket.emit('join_room', 'ecommerce');

    // Listen for new orders
    socket.on('new_order', (orderData) => {
      setOrders(prev => [orderData, ...prev]);
      setLiveMetrics(prev => ({
        ...prev,
        totalOrders: prev.totalOrders + 1,
        pendingOrders: prev.pendingOrders + 1,
        totalRevenue: prev.totalRevenue + (orderData.total || 0)
      }));

      // Add to activity feed
      setActivityFeed(prev => [{
        id: Date.now(),
        type: 'new_order',
        message: `New order #${orderData.orderId} from ${orderData.customer?.name || 'Customer'}`,
        amount: orderData.total,
        timestamp: new Date(),
        icon: ShoppingBag,
        color: 'text-green-600'
      }, ...prev.slice(0, 19)]);

      toast.success(`New order received: #${orderData.orderId}`);
    });

    // Listen for order status updates
    socket.on('order_status_updated', (data) => {
      setOrders(prev => prev.map(order =>
        order.id === data.orderId
          ? { ...order, status: data.status, updatedAt: new Date() }
          : order
      ));

      // Update metrics based on status change
      setLiveMetrics(prev => {
        const updates = { ...prev };
        if (data.status === 'completed') {
          updates.completedOrders += 1;
          updates.pendingOrders = Math.max(0, updates.pendingOrders - 1);
        } else if (data.status === 'cancelled') {
          updates.cancelledOrders += 1;
          updates.pendingOrders = Math.max(0, updates.pendingOrders - 1);
        }
        return updates;
      });

      // Add to activity feed
      setActivityFeed(prev => [{
        id: Date.now(),
        type: 'order_update',
        message: `Order #${data.orderId} status changed to ${data.status}`,
        timestamp: new Date(),
        icon: data.status === 'completed' ? CheckCircle :
              data.status === 'cancelled' ? AlertCircle : Clock,
        color: data.status === 'completed' ? 'text-green-600' :
               data.status === 'cancelled' ? 'text-red-600' : 'text-yellow-600'
      }, ...prev.slice(0, 19)]);
    });

    // Listen for product updates
    socket.on('product_updated', (productData) => {
      setProducts(prev => prev.map(product =>
        product.id === productData.id ? productData : product
      ));

      setActivityFeed(prev => [{
        id: Date.now(),
        type: 'product_update',
        message: `Product "${productData.name}" was updated`,
        timestamp: new Date(),
        icon: Package,
        color: 'text-blue-600'
      }, ...prev.slice(0, 19)]);
    });

    // Listen for customer activity
    socket.on('customer_activity', (data) => {
      setLiveMetrics(prev => ({
        ...prev,
        activeCustomers: data.activeCount || prev.activeCustomers
      }));
    });

    // Listen for real-time metrics updates
    socket.on('metrics_update', (metrics) => {
      setLiveMetrics(prev => ({ ...prev, ...metrics }));
    });

    // Cleanup listeners
    return () => {
      socket.off('new_order');
      socket.off('order_status_updated');
      socket.off('product_updated');
      socket.off('customer_activity');
      socket.off('metrics_update');
      socket.emit('leave_room', 'ecommerce');
    };
  }, [socket, connected]);

  // Computed values with memoization for performance
  const computedMetrics = useMemo(() => {
    const totalOrders = orders.length;
    const totalRevenue = orders.reduce((sum, order) => sum + (order.total || 0), 0);
    const pendingOrders = orders.filter(order => order.status === 'pending').length;
    const completedOrders = orders.filter(order => order.status === 'completed').length;
    const cancelledOrders = orders.filter(order => order.status === 'cancelled').length;
    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

    return {
      totalOrders,
      totalRevenue,
      pendingOrders,
      completedOrders,
      cancelledOrders,
      averageOrderValue,
      conversionRate: totalOrders > 0 ? (completedOrders / totalOrders) * 100 : 0
    };
  }, [orders]);

  // Merge live metrics with computed metrics
  const displayMetrics = useMemo(() => ({
    ...computedMetrics,
    ...liveMetrics,
    // Use computed values as fallback if live metrics are not available
    totalOrders: liveMetrics.totalOrders || computedMetrics.totalOrders,
    totalRevenue: liveMetrics.totalRevenue || computedMetrics.totalRevenue
  }), [computedMetrics, liveMetrics]);

  const handleEditClick = (order) => {
    setEditOrder({ ...order });
    setIsEditOrderOpen(true);
  };

  const handleViewClick = (order) => {
    setViewOrder(order);
    setIsViewOrderOpen(true);
  };

  const handleEditOrderChange = (field, value) => {
    if (field === "customerName" || field === "customerEmail") {
      setEditOrder((prev) => ({
        ...prev,
        customer: {
          ...(prev?.customer || {}),
          [field === "customerName" ? "name" : "email"]: value,
        },
      }));
    } else {
      setEditOrder((prev) => ({
        ...prev,
        [field]: value,
      }));
    }
  };

  const handleUpdateOrder = async () => {
    try {
      const id = editOrder.id || editOrder._id;
      const response = await api.put(`/orders/${id}`, editOrder, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
      });

      setOrders((prev) =>
        prev.map((o) => (o.id === response.data.id || o._id === response.data.id ? response.data : o))
      );
      setIsEditOrderOpen(false);
      setEditOrder(null);
      toast.success("The order has been updated successfully.");

      if (response.data.status === "Paid" || response.data.paymentStatus === "Paid") {
        refreshFinancialData?.();
      }
    } catch (error) {
      toast.error("We couldn't update this order.");
      // eslint-disable-next-line no-console
      console.error("Error updating order:", error);
    }
  };

  // Add Product Handlers
  const handleNewProductChange = (field, value) => {
    setNewProduct((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleAddProduct = async () => {
    const requiredFields = [
      "name",
      "sku",
      "category",
      "stockQuantity",
      "price",
      "supplierName",
    ];
    const hasEmptyField = requiredFields.some(
      (field) => !newProduct[field]?.toString().trim()
    );
    if (hasEmptyField) {
      toast.error("Please fill in all required fields.");
      return;
    }
    const sanitizedProduct = {
      ...newProduct,
      stockQuantity: Number(newProduct.stockQuantity),
      reorderLevel: Number(newProduct.reorderLevel),
      price: Number(newProduct.price),
    };
    try {
      const token = localStorage.getItem("token");
      await api.post("/products", sanitizedProduct, {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });
      setNewProduct(defaultProduct);
      setIsAddProductOpen(false);
      toast.success("Product added successfully!");
    } catch (error) {
      toast.error(error.response?.data?.error || "Error adding product.");
      // eslint-disable-next-line no-console
      console.error("Error adding product:", error);
    }
  };

  // Invite Link Handlers
  const generateInviteLink = () => {
    setIsGenerating(true);
    setTimeout(() => {
      const uniqueCode = Math.random().toString(36).substring(2, 15);
      const link = `${window.location.origin}/client/signup/${uniqueCode}`;
      setInviteLink(link);
      setIsGenerating(false);
      toast.success("Invite link generated. Share this link with your clients.");
    }, 1000);
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(inviteLink);
    toast.success("Invite link copied to clipboard.");
  };

  const shareLink = () => {
    if (navigator.share) {
      navigator
        .share({
          title: "Join My Store",
          text: "You've been invited to access my exclusive online store!",
          url: inviteLink,
        })
        .catch(() => {});
    } else {
      copyToClipboard();
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      Pending: { variant: "secondary" },
      Processing: { variant: "default" },
      Shipped: { variant: "default" },
      Delivered: { variant: "default" },
      Paid: { variant: "default" },
      Cancelled: { variant: "destructive" },
    };
    return <Badge variant={statusConfig[status]?.variant || "default"}>{status}</Badge>;
  };

  // Use displayMetrics for all stats (computed + live data)

  // New E-commerce Handlers
  const handleCartCheckout = (orderData) => {
    setCheckoutData(orderData);
    setIsCartOpen(false);
    setShowCheckout(true);
  };

  const handleCheckoutComplete = (order) => {
    setOrders(prev => [order, ...prev]);
    setShowCheckout(false);
    setCheckoutData(null);
    toast.success(`Order ${order.id} placed successfully!`);
  };

  const handleBackToCart = () => {
    setShowCheckout(false);
    setIsCartOpen(true);
  };

  if (showCheckout && checkoutData) {
    return (
      <Checkout
        orderData={checkoutData}
        onBack={handleBackToCart}
        onComplete={handleCheckoutComplete}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Enhanced Header with Real-time Status */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div>
          <div className="flex items-center gap-3">
            <h1 className="text-3xl font-bold text-foreground">E-Commerce Platform</h1>
            <div className="flex items-center gap-2">
              {connected ? (
                <div className="flex items-center gap-1 px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs">
                  <Zap className="h-3 w-3" />
                  Live
                </div>
              ) : (
                <div className="flex items-center gap-1 px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-xs">
                  <Activity className="h-3 w-3" />
                  Offline
                </div>
              )}
              {loading && (
                <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
              )}
            </div>
          </div>
          <p className="text-muted-foreground">
            Real-time e-commerce management with live updates
            {displayMetrics.totalOrders > 0 && (
              <span className="ml-2 text-green-600 font-medium">
                • {displayMetrics.totalOrders} orders • KES {displayMetrics.totalRevenue.toLocaleString()}
              </span>
            )}
          </p>
        </div>
        <div className="flex flex-wrap gap-2">
          <Button
            variant="outline"
            onClick={refreshData}
            disabled={refreshing}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button
            onClick={() => setIsCartOpen(true)}
            className="flex items-center gap-2"
          >
            <ShoppingCartIcon className="h-4 w-4" />
            View Cart
          </Button>
        </div>
      </div>

      {/* Tabbed Interface */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="orders" className="flex items-center gap-2">
            <Package className="h-4 w-4" />
            Orders
          </TabsTrigger>
          <TabsTrigger value="storefront" className="flex items-center gap-2">
            <Store className="h-4 w-4" />
            Storefront
          </TabsTrigger>
          <TabsTrigger value="customers" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Customers
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview">
          <div className="grid gap-6">
            {/* Real-time Stats Cards */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card className="relative overflow-hidden">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
                  <div className="flex items-center gap-2">
                    <Package className="h-4 w-4 text-muted-foreground" />
                    {connected && <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{displayMetrics.totalOrders}</div>
                  <p className="text-xs text-green-600">
                    {displayMetrics.pendingOrders} pending • {displayMetrics.completedOrders} completed
                  </p>
                </CardContent>
              </Card>

              <Card className="relative overflow-hidden">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Revenue</CardTitle>
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4 text-muted-foreground" />
                    {connected && <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">KES {displayMetrics.totalRevenue.toLocaleString()}</div>
                  <p className="text-xs text-muted-foreground">
                    Real-time revenue tracking
                  </p>
                </CardContent>
              </Card>

              <Card className="relative overflow-hidden">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Avg Order Value</CardTitle>
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-muted-foreground" />
                    {connected && <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">KES {displayMetrics.averageOrderValue.toLocaleString()}</div>
                  <p className="text-xs text-muted-foreground">
                    Per order average
                  </p>
                </CardContent>
              </Card>

              <Card className="relative overflow-hidden">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Active Customers</CardTitle>
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-muted-foreground" />
                    {connected && <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{displayMetrics.activeCustomers}</div>
                  <p className="text-xs text-blue-600">
                    {displayMetrics.conversionRate.toFixed(1)}% conversion rate
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Real-time Activity Feed and Recent Orders Grid */}
            <div className="grid gap-6 lg:grid-cols-3">
              {/* Real-time Activity Feed */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5" />
                    Live Activity
                    {connected && <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />}
                  </CardTitle>
                  <CardDescription>
                    Real-time business activity updates
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {activityFeed.length > 0 ? (
                      activityFeed.map((activity) => {
                        const IconComponent = activity.icon;
                        return (
                          <div key={activity.id} className="flex items-start gap-3 p-2 rounded-lg hover:bg-gray-50">
                            <div className={`p-1 rounded-full bg-gray-100 ${activity.color}`}>
                              <IconComponent className="h-3 w-3" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="text-sm text-gray-900">{activity.message}</p>
                              {activity.amount && (
                                <p className="text-xs text-green-600 font-medium">
                                  KES {activity.amount.toLocaleString()}
                                </p>
                              )}
                              <p className="text-xs text-gray-500">
                                {activity.timestamp.toLocaleTimeString()}
                              </p>
                            </div>
                          </div>
                        );
                      })
                    ) : (
                      <div className="text-center py-8">
                        <Activity className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                        <p className="text-sm text-gray-500">No recent activity</p>
                        <p className="text-xs text-gray-400">Activity will appear here in real-time</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Recent Orders */}
              <Card className="lg:col-span-2">
                <CardHeader>
                  <CardTitle>Recent Orders</CardTitle>
                  <CardDescription>
                    Latest customer orders and their status
                  </CardDescription>
                </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Order ID</TableHead>
                      <TableHead>Customer</TableHead>
                      <TableHead>Total</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {orders.slice(0, 5).map((order) => (
                      <TableRow key={order.id}>
                        <TableCell className="font-medium">{order.id}</TableCell>
                        <TableCell>{order.customerName || 'N/A'}</TableCell>
                        <TableCell>KES {Number(order.total || 0).toLocaleString()}</TableCell>
                        <TableCell>{getStatusBadge(order.status)}</TableCell>
                        <TableCell>{new Date(order.createdAt || Date.now()).toLocaleDateString()}</TableCell>
                        <TableCell>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setViewOrder(order);
                              setIsViewOrderOpen(true);
                            }}
                          >
                            <Eye className="h-3 w-3" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
                {orders.length === 0 && (
                  <div className="text-center py-8">
                    <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">No orders yet</p>
                  </div>
                )}
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        {/* Orders Tab */}
        <TabsContent value="orders">
          <OrderManagement />
        </TabsContent>

        {/* Storefront Tab */}
        <TabsContent value="storefront">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Store className="h-5 w-5" />
                Storefront Management
              </CardTitle>
              <CardDescription>
                Manage your online store appearance and settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="grid gap-4 md:grid-cols-2">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Store Settings</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <Button className="w-full" variant="outline">
                        <Settings className="h-4 w-4 mr-2" />
                        Configure Store
                      </Button>
                      <Button className="w-full" variant="outline">
                        <Eye className="h-4 w-4 mr-2" />
                        Preview Store
                      </Button>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Invite Customers</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <Button
                        onClick={generateInviteLink}
                        disabled={isGenerating}
                        className="w-full"
                      >
                        <Link2 className="h-4 w-4 mr-2" />
                        {isGenerating ? "Generating..." : "Generate Invite Link"}
                      </Button>
                      {inviteLink && (
                        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                          <p className="text-sm font-medium mb-2">Share this link:</p>
                          <div className="flex gap-2">
                            <Input value={inviteLink} readOnly className="text-xs" />
                            <Button size="sm" onClick={copyInviteLink}>
                              <Copy className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Customers Tab */}
        <TabsContent value="customers">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Customer Management
              </CardTitle>
              <CardDescription>
                View and manage your customers
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">Customer management features coming soon</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Shopping Cart Component */}
      <ShoppingCart
        isOpen={isCartOpen}
        onClose={() => setIsCartOpen(false)}
        onCheckout={handleCartCheckout}
      />

      {/* View Order Dialog */}
      {isViewOrderOpen && viewOrder && (
        <Dialog open={isViewOrderOpen} onOpenChange={setIsViewOrderOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Order Details - {viewOrder.id}</DialogTitle>
              <DialogDescription>
                Complete order information
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <h3 className="font-medium mb-2">Customer Information</h3>
                <p className="text-sm text-gray-600">
                  {viewOrder.customerName || 'N/A'}<br />
                  {viewOrder.customerEmail || 'N/A'}
                </p>
              </div>
              <div>
                <h3 className="font-medium mb-2">Order Summary</h3>
                <p className="text-sm text-gray-600">
                  Total: KES {Number(viewOrder.total || 0).toLocaleString()}<br />
                  Status: {viewOrder.status}<br />
                  Date: {new Date(viewOrder.createdAt || Date.now()).toLocaleDateString()}
                </p>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default ECommerce;