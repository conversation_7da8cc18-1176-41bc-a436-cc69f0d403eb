import { useState, useEffect, useCallback } from "react";
import { toast } from "sonner";
import api from "@/lib/api";
import { useAuth } from "@/context/AuthContext";
import { useSocket } from "@/context/SocketContext";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Slider } from "@/components/ui/slider";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import {
  Settings as SettingsIcon,
  User,
  Bell,
  Shield,
  Palette,
  Globe,
  CreditCard,
  Mail,
  Smartphone,
  Save,
  Eye,
  EyeOff,
  Monitor,
  Sun,
  Moon,
  Zap,
  Volume2,
  VolumeX,
  Accessibility,
  Download,
  RefreshCw,
  Loader2,
  CheckCircle,
  AlertCircle,
  Info,
  Camera,
  Check,
  X,
  AlertTriangle,
  HelpCircle,
  ExternalLink,
  Building,
  MapPin,
  Phone,
  Clock,
  DollarSign,
  Languages
} from "lucide-react";
import { useAppTheme, AVAILABLE_THEMES } from "@/context/ThemeContext";
import { useTheme } from "next-themes";
import ThemeCustomizer from "@/components/ThemeCustomizer";
import EmailSettings from "@/components/EmailSettings";

const Settings = () => {
  const { user, isAuthenticated, loading: authLoading } = useAuth();
  const { socket, connected } = useSocket();
  const { theme, setTheme } = useTheme();
  const {
    sidebarCollapsed,
    setSidebarCollapsed,
    animations,
    setAnimations,
    fontSize,
    setFontSize,
    fontSizes,
    borderRadius,
    setBorderRadius,
    borderRadiusOptions,
    compactMode,
    setCompactMode,
    highContrast,
    setHighContrast,
    reducedMotion,
    setReducedMotion,
    customAccentColor,
    setCustomAccentColor,
    soundEnabled,
    setSoundEnabled,
    autoSave,
    setAutoSave
  } = useAppTheme();

  // Component state
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [activeTab, setActiveTab] = useState('general');
  const [profileImage, setProfileImage] = useState(null);
  const [imageUploading, setImageUploading] = useState(false);

  // Settings state with real user data
  const [settings, setSettings] = useState({
    // General Settings
    businessName: user?.businessName || "OmniBiz",
    businessEmail: user?.email || "<EMAIL>",
    businessPhone: user?.phone || "+254 700 123 456",
    businessAddress: user?.address || "123 Business Street, Nairobi",
    timezone: user?.timezone || "Africa/Nairobi",
    currency: user?.currency || "KES",
    language: user?.language || "en",
    website: user?.website || "",
    description: user?.description || "",

    // Profile Settings
    firstName: user?.firstName || "",
    lastName: user?.lastName || "",
    avatar: user?.avatar || "",
    title: user?.title || "",
    bio: user?.bio || "",

    // Notification Settings
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    marketingEmails: false,
    soundNotifications: true,
    desktopNotifications: true,

    // Security Settings
    twoFactorAuth: false,
    sessionTimeout: "30",
    passwordExpiry: "90",
    loginAlerts: true,
    deviceTracking: true,

    // Appearance Settings
    theme: "light",
    sidebarCollapsed: false,
    compactMode: false,
    highContrast: false,
    reducedMotion: false,
    customAccentColor: "#3b82f6",

    // Privacy Settings
    dataSharing: false,
    analytics: true,
    crashReporting: true,
    usageStatistics: false,

    // Performance Settings
    autoSave: true,
    cacheSize: 100,
    backgroundSync: true,

    // Account Settings
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Data fetching functions
  const fetchUserSettings = useCallback(async () => {
    if (!isAuthenticated) return;

    try {
      setLoading(true);
      const response = await api.get('/user/settings');
      const userSettings = response.data;

      setSettings(prev => ({
        ...prev,
        ...userSettings,
        // Merge with user data
        businessName: userSettings.businessName || user?.businessName || prev.businessName,
        businessEmail: userSettings.businessEmail || user?.email || prev.businessEmail,
        firstName: userSettings.firstName || user?.firstName || prev.firstName,
        lastName: userSettings.lastName || user?.lastName || prev.lastName,
        avatar: userSettings.avatar || user?.avatar || prev.avatar,
      }));

    } catch (error) {
      console.error('Error fetching user settings:', error);
      toast.error('Failed to load settings');
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, user]);

  // Save settings function
  const saveSettings = useCallback(async (settingsToSave = settings) => {
    try {
      setSaving(true);
      await api.put('/user/settings', settingsToSave);
      setHasUnsavedChanges(false);
      toast.success('Settings saved successfully!');

      // Emit real-time update if socket is connected
      if (socket && connected) {
        socket.emit('settings_updated', { userId: user?.id, settings: settingsToSave });
      }

    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error('Failed to save settings');
    } finally {
      setSaving(false);
    }
  }, [settings, socket, connected, user?.id]);

  // Auto-save functionality
  useEffect(() => {
    if (hasUnsavedChanges && autoSave) {
      const timeoutId = setTimeout(() => {
        saveSettings();
      }, 2000); // Auto-save after 2 seconds of inactivity

      return () => clearTimeout(timeoutId);
    }
  }, [hasUnsavedChanges, autoSave, saveSettings]);

  // Initial data fetch
  useEffect(() => {
    fetchUserSettings();
  }, [fetchUserSettings]);

  // Handle setting changes
  const handleSettingChange = useCallback((key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
    setHasUnsavedChanges(true);
  }, []);

  // Handle nested setting changes
  const handleNestedSettingChange = useCallback((section, key, value) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value
      }
    }));
    setHasUnsavedChanges(true);
  }, []);

  // Handle profile image upload
  const handleImageUpload = useCallback(async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file type and size
    if (!file.type.startsWith('image/')) {
      toast.error('Please select a valid image file');
      return;
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      toast.error('Image size must be less than 5MB');
      return;
    }

    try {
      setImageUploading(true);
      const formData = new FormData();
      formData.append('avatar', file);

      const response = await api.post('/user/upload-avatar', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      const avatarUrl = response.data.avatarUrl;
      handleSettingChange('avatar', avatarUrl);
      setProfileImage(avatarUrl);
      toast.success('Profile image updated successfully!');

    } catch (error) {
      console.error('Error uploading image:', error);
      toast.error('Failed to upload image');
    } finally {
      setImageUploading(false);
    }
  }, [handleSettingChange]);

  // Handle password change
  const handlePasswordChange = useCallback(async () => {
    if (!settings.currentPassword || !settings.newPassword || !settings.confirmPassword) {
      toast.error('Please fill in all password fields');
      return;
    }

    if (settings.newPassword !== settings.confirmPassword) {
      toast.error('New passwords do not match');
      return;
    }

    if (settings.newPassword.length < 8) {
      toast.error('Password must be at least 8 characters long');
      return;
    }

    try {
      setSaving(true);
      await api.put('/user/change-password', {
        currentPassword: settings.currentPassword,
        newPassword: settings.newPassword,
      });

      // Clear password fields
      setSettings(prev => ({
        ...prev,
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      }));

      toast.success('Password changed successfully!');
    } catch (error) {
      console.error('Error changing password:', error);
      toast.error(error.response?.data?.message || 'Failed to change password');
    } finally {
      setSaving(false);
    }
  }, [settings.currentPassword, settings.newPassword, settings.confirmPassword]);

  useEffect(() => {
    const fetchSettings = async () => {
      if (!isAuthenticated) {
        toast.error("Please log in to view settings.");
        return;
      }

      try {
        const token = localStorage.getItem("token");
        const response = await api.get("/user/profile", {
          headers: { Authorization: `Bearer ${token}` },
        });

        const userData = response.data;

        // Map user data to settings format
        setSettings(prev => ({
          ...prev,
          businessName: userData.businessName || prev.businessName,
          businessEmail: userData.businessEmail || prev.businessEmail,
          businessPhone: userData.businessPhone || prev.businessPhone,
          businessAddress: userData.businessAddress || prev.businessAddress,
          timezone: userData.timezone || prev.timezone,
          currency: userData.currency || prev.currency,
          language: userData.language || prev.language,
          emailNotifications: userData.emailNotifications ?? prev.emailNotifications,
          smsNotifications: userData.smsNotifications ?? prev.smsNotifications,
          pushNotifications: userData.pushNotifications ?? prev.pushNotifications,
          marketingEmails: userData.marketingEmails ?? prev.marketingEmails,
          twoFactorAuth: userData.twoFactorAuth ?? prev.twoFactorAuth,
          sessionTimeout: userData.sessionTimeout?.toString() || prev.sessionTimeout,
          passwordExpiry: userData.passwordExpiry?.toString() || prev.passwordExpiry,
          dataSharing: userData.dataSharing ?? prev.dataSharing,
          analytics: userData.analytics ?? prev.analytics,
        }));
      } catch (error) {
        toast.error("Error fetching settings.");
        console.error("Error fetching settings:", error);
      }
    };

    fetchSettings();
  }, [isAuthenticated]);

  const handleSaveSettings = async (section) => {
    try {
      const token = localStorage.getItem("token");

      // Prepare settings data for the specific section
      let sectionSettings = {};

      switch (section) {
        case 'general':
          sectionSettings = {
            businessName: settings.businessName,
            businessEmail: settings.businessEmail,
            businessPhone: settings.businessPhone,
            businessAddress: settings.businessAddress,
            timezone: settings.timezone,
            currency: settings.currency,
            language: settings.language
          };
          break;
        case 'notifications':
          sectionSettings = {
            emailNotifications: settings.emailNotifications,
            smsNotifications: settings.smsNotifications,
            pushNotifications: settings.pushNotifications,
            marketingEmails: settings.marketingEmails
          };
          break;
        case 'security':
          sectionSettings = {
            twoFactorAuth: settings.twoFactorAuth,
            sessionTimeout: parseInt(settings.sessionTimeout),
            passwordExpiry: parseInt(settings.passwordExpiry)
          };
          break;
        case 'privacy':
          sectionSettings = {
            dataSharing: settings.dataSharing,
            analytics: settings.analytics
          };
          break;
      }

      await api.put("/user/settings", {
        section,
        settings: sectionSettings
      }, {
        headers: { Authorization: `Bearer ${token}` },
      });

      toast.success(`${section} settings saved successfully!`);
    } catch (error) {
      toast.error(`Error saving ${section} settings.`);
      console.error(`Error saving ${section} settings:`, error);
    }
  };



  if (authLoading || loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading settings...</span>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <AlertCircle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
            <CardTitle>Authentication Required</CardTitle>
            <CardDescription>
              Please log in to access your settings and preferences.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6 max-w-6xl mx-auto">
      {/* Enhanced Header */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div>
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <SettingsIcon className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-foreground">Settings</h1>
              <p className="text-muted-foreground">
                Manage your account preferences and application settings
                {connected && (
                  <span className="ml-2 inline-flex items-center gap-1 text-green-600 text-sm">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                    Live sync enabled
                  </span>
                )}
              </p>
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {hasUnsavedChanges && (
            <Badge variant="outline" className="text-yellow-600 border-yellow-300">
              <AlertCircle className="h-3 w-3 mr-1" />
              Unsaved changes
            </Badge>
          )}
          <Button
            onClick={() => saveSettings()}
            disabled={saving || !hasUnsavedChanges}
            className="flex items-center gap-2"
          >
            {saving ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Save className="h-4 w-4" />
            )}
            {saving ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </div>

      {/* Enhanced Tabs with Better Styling */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <div className="border-b border-gray-200">
          <TabsList className="grid w-full grid-cols-7 h-auto p-1 bg-gray-50 rounded-lg">
            <TabsTrigger
              value="general"
              className="flex items-center gap-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
            >
              <Building className="h-4 w-4" />
              <span className="hidden sm:inline">General</span>
            </TabsTrigger>
            <TabsTrigger
              value="profile"
              className="flex items-center gap-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
            >
              <User className="h-4 w-4" />
              <span className="hidden sm:inline">Profile</span>
            </TabsTrigger>
            <TabsTrigger
              value="email"
              className="flex items-center gap-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
            >
              <Mail className="h-4 w-4" />
              <span className="hidden sm:inline">Email</span>
            </TabsTrigger>
            <TabsTrigger
              value="notifications"
              className="flex items-center gap-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
            >
              <Bell className="h-4 w-4" />
              <span className="hidden sm:inline">Notifications</span>
            </TabsTrigger>
            <TabsTrigger
              value="security"
              className="flex items-center gap-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
            >
              <Shield className="h-4 w-4" />
              <span className="hidden sm:inline">Security</span>
            </TabsTrigger>
            <TabsTrigger
              value="appearance"
              className="flex items-center gap-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
            >
              <Palette className="h-4 w-4" />
              <span className="hidden sm:inline">Appearance</span>
            </TabsTrigger>
            <TabsTrigger
              value="privacy"
              className="flex items-center gap-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
            >
              <Shield className="h-4 w-4" />
              <span className="hidden sm:inline">Privacy</span>
            </TabsTrigger>
          </TabsList>
        </div>

        {/* Enhanced General Settings */}
        <TabsContent value="general" className="space-y-6">
          <Card className="shadow-sm">
            <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5 text-blue-600" />
                Business Information
              </CardTitle>
              <CardDescription>
                Configure your business details and operational preferences
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="businessName" className="text-sm font-medium flex items-center gap-2">
                    <Building className="h-4 w-4" />
                    Business Name
                  </Label>
                  <Input
                    id="businessName"
                    value={settings.businessName}
                    onChange={(e) => handleSettingChange('businessName', e.target.value)}
                    placeholder="Enter your business name"
                    className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="businessEmail" className="text-sm font-medium flex items-center gap-2">
                    <Mail className="h-4 w-4" />
                    Business Email
                  </Label>
                  <Input
                    id="businessEmail"
                    type="email"
                    value={settings.businessEmail}
                    onChange={(e) => handleSettingChange('businessEmail', e.target.value)}
                    placeholder="<EMAIL>"
                    className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="businessPhone" className="text-sm font-medium flex items-center gap-2">
                    <Phone className="h-4 w-4" />
                    Business Phone
                  </Label>
                  <Input
                    id="businessPhone"
                    value={settings.businessPhone}
                    onChange={(e) => handleSettingChange('businessPhone', e.target.value)}
                    placeholder="+254 700 123 456"
                    className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="timezone" className="text-sm font-medium flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    Timezone
                  </Label>
                  <Select
                    value={settings.timezone}
                    onValueChange={(value) => handleSettingChange('timezone', value)}
                  >
                    <SelectTrigger className="transition-all duration-200 focus:ring-2 focus:ring-blue-500">
                      <SelectValue placeholder="Select timezone" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Africa/Nairobi">🇰🇪 Africa/Nairobi (EAT)</SelectItem>
                      <SelectItem value="UTC">🌍 UTC</SelectItem>
                      <SelectItem value="America/New_York">🇺🇸 America/New_York (EST)</SelectItem>
                      <SelectItem value="Europe/London">🇬🇧 Europe/London (GMT)</SelectItem>
                      <SelectItem value="Asia/Dubai">🇦🇪 Asia/Dubai (GST)</SelectItem>
                      <SelectItem value="Asia/Tokyo">🇯🇵 Asia/Tokyo (JST)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="currency" className="text-sm font-medium flex items-center gap-2">
                    <DollarSign className="h-4 w-4" />
                    Currency
                  </Label>
                  <Select
                    value={settings.currency}
                    onValueChange={(value) => handleSettingChange('currency', value)}
                  >
                    <SelectTrigger className="transition-all duration-200 focus:ring-2 focus:ring-blue-500">
                      <SelectValue placeholder="Select currency" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="KES">KES - Kenyan Shilling</SelectItem>
                      <SelectItem value="USD">USD - US Dollar</SelectItem>
                      <SelectItem value="EUR">EUR - Euro</SelectItem>
                      <SelectItem value="GBP">GBP - British Pound</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="language">Language</Label>
                  <Select
                    value={settings.language}
                    onValueChange={(value) =>
                      setSettings({ ...settings, language: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="en">English</SelectItem>
                      <SelectItem value="sw">Swahili</SelectItem>
                      <SelectItem value="fr">French</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div>
                <Label htmlFor="businessAddress">Business Address</Label>
                <Textarea
                  id="businessAddress"
                  value={settings.businessAddress}
                  onChange={(e) =>
                    setSettings({ ...settings, businessAddress: e.target.value })
                  }
                />
              </div>
              <Button onClick={() => handleSaveSettings("general")}>
                <Save className="mr-2 h-4 w-4" />
                Save General Settings
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Email Settings */}
        <TabsContent value="email">
          <EmailSettings />
        </TabsContent>

        {/* Notification Settings */}
        <TabsContent value="notifications">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                Notification Settings
              </CardTitle>
              <CardDescription>
                Configure how you receive notifications
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Email Notifications</Label>
                    <p className="text-sm text-muted-foreground">
                      Receive notifications via email
                    </p>
                  </div>
                  <Switch
                    checked={settings.emailNotifications}
                    onCheckedChange={(checked) =>
                      setSettings({ ...settings, emailNotifications: checked })
                    }
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>SMS Notifications</Label>
                    <p className="text-sm text-muted-foreground">
                      Receive notifications via SMS
                    </p>
                  </div>
                  <Switch
                    checked={settings.smsNotifications}
                    onCheckedChange={(checked) =>
                      setSettings({ ...settings, smsNotifications: checked })
                    }
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Push Notifications</Label>
                    <p className="text-sm text-muted-foreground">
                      Receive push notifications in browser
                    </p>
                  </div>
                  <Switch
                    checked={settings.pushNotifications}
                    onCheckedChange={(checked) =>
                      setSettings({ ...settings, pushNotifications: checked })
                    }
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Marketing Emails</Label>
                    <p className="text-sm text-muted-foreground">
                      Receive marketing and promotional emails
                    </p>
                  </div>
                  <Switch
                    checked={settings.marketingEmails}
                    onCheckedChange={(checked) =>
                      setSettings({ ...settings, marketingEmails: checked })
                    }
                  />
                </div>
              </div>
              <Button onClick={() => handleSaveSettings("notifications")}>
                <Save className="mr-2 h-4 w-4" />
                Save Notification Settings
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Security Settings */}
        <TabsContent value="security">
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Security Settings
                </CardTitle>
                <CardDescription>
                  Manage your account security preferences
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Two-Factor Authentication</Label>
                      <p className="text-sm text-muted-foreground">
                        Add an extra layer of security to your account
                      </p>
                    </div>
                    <Switch
                      checked={settings.twoFactorAuth}
                      onCheckedChange={(checked) =>
                        setSettings({ ...settings, twoFactorAuth: checked })
                      }
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="sessionTimeout">Session Timeout (minutes)</Label>
                      <Input
                        id="sessionTimeout"
                        type="number"
                        value={settings.sessionTimeout}
                        onChange={(e) =>
                          setSettings({ ...settings, sessionTimeout: e.target.value })
                        }
                      />
                    </div>
                    <div>
                      <Label htmlFor="passwordExpiry">Password Expiry (days)</Label>
                      <Input
                        id="passwordExpiry"
                        type="number"
                        value={settings.passwordExpiry}
                        onChange={(e) =>
                          setSettings({ ...settings, passwordExpiry: e.target.value })
                        }
                      />
                    </div>
                  </div>
                </div>
                <Button onClick={() => handleSaveSettings("security")}>
                  <Save className="mr-2 h-4 w-4" />
                  Save Security Settings
                </Button>
              </CardContent>
            </Card>

            {/* Change Password */}
            <Card>
              <CardHeader>
                <CardTitle>Change Password</CardTitle>
                <CardDescription>
                  Update your account password
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="currentPassword">Current Password</Label>
                  <div className="relative">
                    <Input
                      id="currentPassword"
                      type={showPassword ? "text" : "password"}
                      value={settings.currentPassword}
                      onChange={(e) =>
                        setSettings({ ...settings, currentPassword: e.target.value })
                      }
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>
                <div>
                  <Label htmlFor="newPassword">New Password</Label>
                  <Input
                    id="newPassword"
                    type="password"
                    value={settings.newPassword}
                    onChange={(e) =>
                      setSettings({ ...settings, newPassword: e.target.value })
                    }
                  />
                </div>
                <div>
                  <Label htmlFor="confirmPassword">Confirm New Password</Label>
                  <Input
                    id="confirmPassword"
                    type="password"
                    value={settings.confirmPassword}
                    onChange={(e) =>
                      setSettings({ ...settings, confirmPassword: e.target.value })
                    }
                  />
                </div>
                <Button onClick={handlePasswordChange}>
                  <Save className="mr-2 h-4 w-4" />
                  Change Password
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Appearance Settings */}
        <TabsContent value="appearance">
          <ThemeCustomizer />
        </TabsContent>

        {/* Privacy Settings */}
        <TabsContent value="privacy">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Privacy Settings
              </CardTitle>
              <CardDescription>
                Control your data and privacy preferences
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Data Sharing</Label>
                    <p className="text-sm text-muted-foreground">
                      Allow sharing of anonymized data for product improvement
                    </p>
                  </div>
                  <Switch
                    checked={settings.dataSharing}
                    onCheckedChange={(checked) =>
                      setSettings({ ...settings, dataSharing: checked })
                    }
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Analytics</Label>
                    <p className="text-sm text-muted-foreground">
                      Allow collection of usage analytics
                    </p>
                  </div>
                  <Switch
                    checked={settings.analytics}
                    onCheckedChange={(checked) =>
                      setSettings({ ...settings, analytics: checked })
                    }
                  />
                </div>
              </div>
              <Button onClick={() => handleSaveSettings("privacy")}>
                <Save className="mr-2 h-4 w-4" />
                Save Privacy Settings
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Settings;
