import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  Mail, 
  Server, 
  Shield, 
  Send, 
  Eye, 
  EyeOff, 
  TestTube, 
  Save,
  AlertCircle,
  CheckCircle,
  Settings,
  Template,
  Zap,
  Clock
} from 'lucide-react';
import { toast } from 'sonner';

const EmailSettings = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [testingConnection, setTestingConnection] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState(null);
  
  const [emailConfig, setEmailConfig] = useState({
    // SMTP Configuration
    smtpHost: '',
    smtpPort: '587',
    smtpSecurity: 'tls', // none, tls, ssl
    smtpUsername: '',
    smtpPassword: '',
    fromEmail: '',
    fromName: '',
    replyToEmail: '',
    
    // Email Templates
    templates: {
      welcome: {
        subject: 'Welcome to {{businessName}}!',
        body: 'Dear {{customerName}},\n\nWelcome to {{businessName}}! We\'re excited to have you on board.\n\nBest regards,\n{{businessName}} Team'
      },
      orderConfirmation: {
        subject: 'Order Confirmation - #{{orderNumber}}',
        body: 'Dear {{customerName}},\n\nThank you for your order #{{orderNumber}}.\n\nOrder Details:\n{{orderDetails}}\n\nTotal: {{orderTotal}}\n\nBest regards,\n{{businessName}} Team'
      },
      passwordReset: {
        subject: 'Password Reset Request',
        body: 'Dear {{customerName}},\n\nYou requested a password reset. Click the link below to reset your password:\n{{resetLink}}\n\nIf you didn\'t request this, please ignore this email.\n\nBest regards,\n{{businessName}} Team'
      },
      invoice: {
        subject: 'Invoice #{{invoiceNumber}} from {{businessName}}',
        body: 'Dear {{customerName}},\n\nPlease find your invoice #{{invoiceNumber}} attached.\n\nAmount Due: {{invoiceAmount}}\nDue Date: {{dueDate}}\n\nBest regards,\n{{businessName}} Team'
      }
    },
    
    // Automation Settings
    automation: {
      welcomeEmail: true,
      orderConfirmation: true,
      abandonedCart: true,
      abandonedCartDelay: 24, // hours
      invoiceReminders: true,
      invoiceReminderDays: [7, 3, 1], // days before due date
      marketingEmails: false,
      newsletterFrequency: 'weekly' // weekly, monthly, quarterly
    },
    
    // Notification Settings
    notifications: {
      newOrder: true,
      lowStock: true,
      customerSignup: true,
      paymentReceived: true,
      systemAlerts: true
    }
  });

  const handleConfigChange = (section, field, value) => {
    setEmailConfig(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  };

  const handleDirectChange = (field, value) => {
    setEmailConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const testSMTPConnection = async () => {
    setTestingConnection(true);
    setConnectionStatus(null);
    
    try {
      // Simulate SMTP connection test
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // In a real implementation, this would make an API call to test SMTP
      const isValid = emailConfig.smtpHost && emailConfig.smtpUsername && emailConfig.smtpPassword;
      
      if (isValid) {
        setConnectionStatus('success');
        toast.success('SMTP connection successful!');
      } else {
        setConnectionStatus('error');
        toast.error('SMTP connection failed. Please check your settings.');
      }
    } catch (error) {
      setConnectionStatus('error');
      toast.error('Failed to test SMTP connection.');
    } finally {
      setTestingConnection(false);
    }
  };

  const sendTestEmail = async () => {
    try {
      // Simulate sending test email
      await new Promise(resolve => setTimeout(resolve, 1500));
      toast.success('Test email sent successfully!');
    } catch (error) {
      toast.error('Failed to send test email.');
    }
  };

  const saveEmailSettings = async () => {
    try {
      // Simulate saving settings
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success('Email settings saved successfully!');
    } catch (error) {
      toast.error('Failed to save email settings.');
    }
  };

  const resetToDefaults = () => {
    setEmailConfig(prev => ({
      ...prev,
      templates: {
        welcome: {
          subject: 'Welcome to {{businessName}}!',
          body: 'Dear {{customerName}},\n\nWelcome to {{businessName}}! We\'re excited to have you on board.\n\nBest regards,\n{{businessName}} Team'
        },
        orderConfirmation: {
          subject: 'Order Confirmation - #{{orderNumber}}',
          body: 'Dear {{customerName}},\n\nThank you for your order #{{orderNumber}}.\n\nOrder Details:\n{{orderDetails}}\n\nTotal: {{orderTotal}}\n\nBest regards,\n{{businessName}} Team'
        },
        passwordReset: {
          subject: 'Password Reset Request',
          body: 'Dear {{customerName}},\n\nYou requested a password reset. Click the link below to reset your password:\n{{resetLink}}\n\nIf you didn\'t request this, please ignore this email.\n\nBest regards,\n{{businessName}} Team'
        },
        invoice: {
          subject: 'Invoice #{{invoiceNumber}} from {{businessName}}',
          body: 'Dear {{customerName}},\n\nPlease find your invoice #{{invoiceNumber}} attached.\n\nAmount Due: {{invoiceAmount}}\nDue Date: {{dueDate}}\n\nBest regards,\n{{businessName}} Team'
        }
      }
    }));
    toast.success('Templates reset to defaults');
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Email Settings</h2>
          <p className="text-gray-600">Configure SMTP, templates, and automation</p>
        </div>
        <Button onClick={saveEmailSettings} className="flex items-center gap-2">
          <Save className="h-4 w-4" />
          Save Settings
        </Button>
      </div>

      <Tabs defaultValue="smtp" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="smtp" className="flex items-center gap-2">
            <Server className="h-4 w-4" />
            SMTP
          </TabsTrigger>
          <TabsTrigger value="templates" className="flex items-center gap-2">
            <Template className="h-4 w-4" />
            Templates
          </TabsTrigger>
          <TabsTrigger value="automation" className="flex items-center gap-2">
            <Zap className="h-4 w-4" />
            Automation
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center gap-2">
            <Mail className="h-4 w-4" />
            Notifications
          </TabsTrigger>
        </TabsList>

        <TabsContent value="smtp">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Server className="h-5 w-5" />
                SMTP Configuration
              </CardTitle>
              <CardDescription>
                Configure your SMTP server settings for sending emails
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="smtpHost">SMTP Host</Label>
                  <Input
                    id="smtpHost"
                    placeholder="smtp.gmail.com"
                    value={emailConfig.smtpHost}
                    onChange={(e) => handleDirectChange('smtpHost', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="smtpPort">SMTP Port</Label>
                  <Input
                    id="smtpPort"
                    placeholder="587"
                    value={emailConfig.smtpPort}
                    onChange={(e) => handleDirectChange('smtpPort', e.target.value)}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="smtpSecurity">Security</Label>
                <Select
                  value={emailConfig.smtpSecurity}
                  onValueChange={(value) => handleDirectChange('smtpSecurity', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">None</SelectItem>
                    <SelectItem value="tls">TLS</SelectItem>
                    <SelectItem value="ssl">SSL</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="smtpUsername">Username</Label>
                  <Input
                    id="smtpUsername"
                    placeholder="<EMAIL>"
                    value={emailConfig.smtpUsername}
                    onChange={(e) => handleDirectChange('smtpUsername', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="smtpPassword">Password</Label>
                  <div className="relative">
                    <Input
                      id="smtpPassword"
                      type={showPassword ? "text" : "password"}
                      placeholder="Your app password"
                      value={emailConfig.smtpPassword}
                      onChange={(e) => handleDirectChange('smtpPassword', e.target.value)}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="fromEmail">From Email</Label>
                  <Input
                    id="fromEmail"
                    placeholder="<EMAIL>"
                    value={emailConfig.fromEmail}
                    onChange={(e) => handleDirectChange('fromEmail', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="fromName">From Name</Label>
                  <Input
                    id="fromName"
                    placeholder="Your Business Name"
                    value={emailConfig.fromName}
                    onChange={(e) => handleDirectChange('fromName', e.target.value)}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="replyToEmail">Reply-To Email</Label>
                <Input
                  id="replyToEmail"
                  placeholder="<EMAIL>"
                  value={emailConfig.replyToEmail}
                  onChange={(e) => handleDirectChange('replyToEmail', e.target.value)}
                />
              </div>

              <div className="flex gap-4">
                <Button
                  onClick={testSMTPConnection}
                  disabled={testingConnection}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <TestTube className="h-4 w-4" />
                  {testingConnection ? 'Testing...' : 'Test Connection'}
                </Button>

                <Button
                  onClick={sendTestEmail}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <Send className="h-4 w-4" />
                  Send Test Email
                </Button>
              </div>

              {connectionStatus && (
                <div className={`flex items-center gap-2 p-3 rounded-lg ${
                  connectionStatus === 'success'
                    ? 'bg-green-50 text-green-700 border border-green-200'
                    : 'bg-red-50 text-red-700 border border-red-200'
                }`}>
                  {connectionStatus === 'success' ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <AlertCircle className="h-4 w-4" />
                  )}
                  <span className="text-sm">
                    {connectionStatus === 'success'
                      ? 'SMTP connection successful!'
                      : 'SMTP connection failed. Please check your settings.'}
                  </span>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="templates">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Template className="h-5 w-5" />
                Email Templates
              </CardTitle>
              <CardDescription>
                Customize email templates for different scenarios
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex justify-end">
                <Button onClick={resetToDefaults} variant="outline" size="sm">
                  Reset to Defaults
                </Button>
              </div>

              <div className="space-y-6">
                {Object.entries(emailConfig.templates).map(([templateKey, template]) => (
                  <Card key={templateKey} className="border-l-4 border-l-blue-500">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg capitalize">
                        {templateKey.replace(/([A-Z])/g, ' $1').trim()}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <Label htmlFor={`${templateKey}-subject`}>Subject</Label>
                        <Input
                          id={`${templateKey}-subject`}
                          value={template.subject}
                          onChange={(e) => handleConfigChange('templates', templateKey, {
                            ...template,
                            subject: e.target.value
                          })}
                          placeholder="Email subject line"
                        />
                      </div>
                      <div>
                        <Label htmlFor={`${templateKey}-body`}>Body</Label>
                        <Textarea
                          id={`${templateKey}-body`}
                          value={template.body}
                          onChange={(e) => handleConfigChange('templates', templateKey, {
                            ...template,
                            body: e.target.value
                          })}
                          placeholder="Email body content"
                          rows={6}
                        />
                      </div>
                      <div className="text-xs text-gray-500">
                        <p className="font-medium mb-1">Available variables:</p>
                        <div className="flex flex-wrap gap-2">
                          {templateKey === 'welcome' && (
                            <>
                              <Badge variant="outline">{'{{customerName}}'}</Badge>
                              <Badge variant="outline">{'{{businessName}}'}</Badge>
                            </>
                          )}
                          {templateKey === 'orderConfirmation' && (
                            <>
                              <Badge variant="outline">{'{{customerName}}'}</Badge>
                              <Badge variant="outline">{'{{orderNumber}}'}</Badge>
                              <Badge variant="outline">{'{{orderDetails}}'}</Badge>
                              <Badge variant="outline">{'{{orderTotal}}'}</Badge>
                              <Badge variant="outline">{'{{businessName}}'}</Badge>
                            </>
                          )}
                          {templateKey === 'passwordReset' && (
                            <>
                              <Badge variant="outline">{'{{customerName}}'}</Badge>
                              <Badge variant="outline">{'{{resetLink}}'}</Badge>
                              <Badge variant="outline">{'{{businessName}}'}</Badge>
                            </>
                          )}
                          {templateKey === 'invoice' && (
                            <>
                              <Badge variant="outline">{'{{customerName}}'}</Badge>
                              <Badge variant="outline">{'{{invoiceNumber}}'}</Badge>
                              <Badge variant="outline">{'{{invoiceAmount}}'}</Badge>
                              <Badge variant="outline">{'{{dueDate}}'}</Badge>
                              <Badge variant="outline">{'{{businessName}}'}</Badge>
                            </>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="automation">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Email Automation
              </CardTitle>
              <CardDescription>
                Configure automated email triggers and schedules
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Welcome Email</Label>
                    <p className="text-sm text-muted-foreground">
                      Send welcome email to new customers
                    </p>
                  </div>
                  <Switch
                    checked={emailConfig.automation.welcomeEmail}
                    onCheckedChange={(checked) =>
                      handleConfigChange('automation', 'welcomeEmail', checked)
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Order Confirmation</Label>
                    <p className="text-sm text-muted-foreground">
                      Send confirmation email for new orders
                    </p>
                  </div>
                  <Switch
                    checked={emailConfig.automation.orderConfirmation}
                    onCheckedChange={(checked) =>
                      handleConfigChange('automation', 'orderConfirmation', checked)
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Abandoned Cart Recovery</Label>
                    <p className="text-sm text-muted-foreground">
                      Send reminder emails for abandoned carts
                    </p>
                  </div>
                  <Switch
                    checked={emailConfig.automation.abandonedCart}
                    onCheckedChange={(checked) =>
                      handleConfigChange('automation', 'abandonedCart', checked)
                    }
                  />
                </div>

                {emailConfig.automation.abandonedCart && (
                  <div className="ml-6 space-y-2">
                    <Label htmlFor="abandonedCartDelay">Delay (hours)</Label>
                    <Input
                      id="abandonedCartDelay"
                      type="number"
                      min="1"
                      max="168"
                      value={emailConfig.automation.abandonedCartDelay}
                      onChange={(e) =>
                        handleConfigChange('automation', 'abandonedCartDelay', parseInt(e.target.value))
                      }
                      className="w-32"
                    />
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Invoice Reminders</Label>
                    <p className="text-sm text-muted-foreground">
                      Send reminders for unpaid invoices
                    </p>
                  </div>
                  <Switch
                    checked={emailConfig.automation.invoiceReminders}
                    onCheckedChange={(checked) =>
                      handleConfigChange('automation', 'invoiceReminders', checked)
                    }
                  />
                </div>

                {emailConfig.automation.invoiceReminders && (
                  <div className="ml-6 space-y-2">
                    <Label>Reminder Schedule (days before due date)</Label>
                    <div className="flex gap-2">
                      {emailConfig.automation.invoiceReminderDays.map((day, index) => (
                        <Input
                          key={index}
                          type="number"
                          min="1"
                          max="30"
                          value={day}
                          onChange={(e) => {
                            const newDays = [...emailConfig.automation.invoiceReminderDays];
                            newDays[index] = parseInt(e.target.value);
                            handleConfigChange('automation', 'invoiceReminderDays', newDays);
                          }}
                          className="w-20"
                        />
                      ))}
                    </div>
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Marketing Emails</Label>
                    <p className="text-sm text-muted-foreground">
                      Send promotional and marketing emails
                    </p>
                  </div>
                  <Switch
                    checked={emailConfig.automation.marketingEmails}
                    onCheckedChange={(checked) =>
                      handleConfigChange('automation', 'marketingEmails', checked)
                    }
                  />
                </div>

                {emailConfig.automation.marketingEmails && (
                  <div className="ml-6 space-y-2">
                    <Label htmlFor="newsletterFrequency">Newsletter Frequency</Label>
                    <Select
                      value={emailConfig.automation.newsletterFrequency}
                      onValueChange={(value) =>
                        handleConfigChange('automation', 'newsletterFrequency', value)
                      }
                    >
                      <SelectTrigger className="w-48">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="weekly">Weekly</SelectItem>
                        <SelectItem value="monthly">Monthly</SelectItem>
                        <SelectItem value="quarterly">Quarterly</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="h-5 w-5" />
                Email Notifications
              </CardTitle>
              <CardDescription>
                Configure which events trigger email notifications to administrators
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>New Order Notifications</Label>
                    <p className="text-sm text-muted-foreground">
                      Get notified when new orders are placed
                    </p>
                  </div>
                  <Switch
                    checked={emailConfig.notifications.newOrder}
                    onCheckedChange={(checked) =>
                      handleConfigChange('notifications', 'newOrder', checked)
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Low Stock Alerts</Label>
                    <p className="text-sm text-muted-foreground">
                      Get notified when products are running low
                    </p>
                  </div>
                  <Switch
                    checked={emailConfig.notifications.lowStock}
                    onCheckedChange={(checked) =>
                      handleConfigChange('notifications', 'lowStock', checked)
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Customer Signup</Label>
                    <p className="text-sm text-muted-foreground">
                      Get notified when new customers register
                    </p>
                  </div>
                  <Switch
                    checked={emailConfig.notifications.customerSignup}
                    onCheckedChange={(checked) =>
                      handleConfigChange('notifications', 'customerSignup', checked)
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Payment Received</Label>
                    <p className="text-sm text-muted-foreground">
                      Get notified when payments are received
                    </p>
                  </div>
                  <Switch
                    checked={emailConfig.notifications.paymentReceived}
                    onCheckedChange={(checked) =>
                      handleConfigChange('notifications', 'paymentReceived', checked)
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>System Alerts</Label>
                    <p className="text-sm text-muted-foreground">
                      Get notified about system issues and updates
                    </p>
                  </div>
                  <Switch
                    checked={emailConfig.notifications.systemAlerts}
                    onCheckedChange={(checked) =>
                      handleConfigChange('notifications', 'systemAlerts', checked)
                    }
                  />
                </div>
              </div>

              <div className="pt-4 border-t">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="flex items-start gap-3">
                    <Mail className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-blue-900">Email Delivery Tips</h4>
                      <ul className="text-sm text-blue-700 mt-2 space-y-1">
                        <li>• Use a dedicated email address for notifications</li>
                        <li>• Test your SMTP settings regularly</li>
                        <li>• Monitor your email delivery rates</li>
                        <li>• Keep your templates up to date</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default EmailSettings;
