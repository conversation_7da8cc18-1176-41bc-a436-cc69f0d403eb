import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  ShoppingCart, 
  Plus, 
  Minus, 
  Trash2, 
  CreditCard, 
  Truck,
  Tag,
  Gift,
  ArrowRight,
  X
} from 'lucide-react';
import { toast } from 'sonner';

const ShoppingCart = ({ isOpen, onClose, onCheckout }) => {
  const [cartItems, setCartItems] = useState([
    {
      id: 1,
      name: 'Premium Hair Treatment',
      price: 2500,
      quantity: 1,
      image: '💇',
      category: 'Services',
      variant: 'Standard',
      maxQuantity: 5
    },
    {
      id: 2,
      name: 'Organic Shampoo',
      price: 800,
      quantity: 2,
      image: '🧴',
      category: 'Products',
      variant: '500ml',
      maxQuantity: 10
    }
  ]);

  const [promoCode, setPromoCode] = useState('');
  const [appliedPromo, setAppliedPromo] = useState(null);
  const [shippingMethod, setShippingMethod] = useState('standard');

  const shippingOptions = {
    standard: { name: 'Standard Delivery', price: 200, days: '3-5' },
    express: { name: 'Express Delivery', price: 500, days: '1-2' },
    pickup: { name: 'Store Pickup', price: 0, days: 'Same day' }
  };

  const promoCodes = {
    'SAVE10': { discount: 0.1, type: 'percentage', description: '10% off' },
    'FIRST50': { discount: 50, type: 'fixed', description: 'KES 50 off' },
    'FREESHIP': { discount: 0, type: 'shipping', description: 'Free shipping' }
  };

  const updateQuantity = (id, newQuantity) => {
    if (newQuantity < 1) {
      removeItem(id);
      return;
    }

    setCartItems(items => 
      items.map(item => {
        if (item.id === id) {
          const quantity = Math.min(newQuantity, item.maxQuantity);
          return { ...item, quantity };
        }
        return item;
      })
    );
  };

  const removeItem = (id) => {
    setCartItems(items => items.filter(item => item.id !== id));
    toast.success('Item removed from cart');
  };

  const applyPromoCode = () => {
    const promo = promoCodes[promoCode.toUpperCase()];
    if (promo) {
      setAppliedPromo({ code: promoCode.toUpperCase(), ...promo });
      toast.success(`Promo code applied: ${promo.description}`);
    } else {
      toast.error('Invalid promo code');
    }
    setPromoCode('');
  };

  const removePromoCode = () => {
    setAppliedPromo(null);
    toast.success('Promo code removed');
  };

  const subtotal = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  
  const discount = appliedPromo ? (
    appliedPromo.type === 'percentage' ? subtotal * appliedPromo.discount :
    appliedPromo.type === 'fixed' ? appliedPromo.discount : 0
  ) : 0;

  const shipping = appliedPromo?.type === 'shipping' ? 0 : shippingOptions[shippingMethod].price;
  const total = subtotal - discount + shipping;

  const handleCheckout = () => {
    const orderData = {
      items: cartItems,
      subtotal,
      discount,
      shipping,
      total,
      promoCode: appliedPromo?.code,
      shippingMethod
    };
    onCheckout(orderData);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-end">
      <div className="bg-white w-full max-w-md h-full overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold flex items-center gap-2">
              <ShoppingCart className="h-5 w-5" />
              Shopping Cart ({cartItems.length})
            </h2>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>

          {cartItems.length === 0 ? (
            <div className="text-center py-12">
              <ShoppingCart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Your cart is empty</p>
              <Button onClick={onClose} className="mt-4">
                Continue Shopping
              </Button>
            </div>
          ) : (
            <div className="space-y-6">
              {/* Cart Items */}
              <div className="space-y-4">
                {cartItems.map((item) => (
                  <Card key={item.id} className="p-4">
                    <div className="flex gap-3">
                      <div className="text-2xl">{item.image}</div>
                      <div className="flex-1">
                        <h3 className="font-medium">{item.name}</h3>
                        <p className="text-sm text-gray-500">{item.variant}</p>
                        <div className="flex items-center justify-between mt-2">
                          <span className="font-medium">KES {item.price.toLocaleString()}</span>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => updateQuantity(item.id, item.quantity - 1)}
                            >
                              <Minus className="h-3 w-3" />
                            </Button>
                            <span className="w-8 text-center">{item.quantity}</span>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => updateQuantity(item.id, item.quantity + 1)}
                              disabled={item.quantity >= item.maxQuantity}
                            >
                              <Plus className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeItem(item.id)}
                              className="text-red-500 hover:text-red-700"
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>

              {/* Promo Code */}
              <Card className="p-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Tag className="h-4 w-4" />
                    <span className="font-medium">Promo Code</span>
                  </div>
                  {appliedPromo ? (
                    <div className="flex items-center justify-between bg-green-50 p-2 rounded">
                      <div className="flex items-center gap-2">
                        <Gift className="h-4 w-4 text-green-600" />
                        <span className="text-sm font-medium">{appliedPromo.code}</span>
                        <span className="text-sm text-green-600">{appliedPromo.description}</span>
                      </div>
                      <Button variant="ghost" size="sm" onClick={removePromoCode}>
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ) : (
                    <div className="flex gap-2">
                      <Input
                        placeholder="Enter promo code"
                        value={promoCode}
                        onChange={(e) => setPromoCode(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && applyPromoCode()}
                      />
                      <Button onClick={applyPromoCode} disabled={!promoCode}>
                        Apply
                      </Button>
                    </div>
                  )}
                </div>
              </Card>

              {/* Shipping Options */}
              <Card className="p-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Truck className="h-4 w-4" />
                    <span className="font-medium">Shipping</span>
                  </div>
                  <div className="space-y-2">
                    {Object.entries(shippingOptions).map(([key, option]) => (
                      <label key={key} className="flex items-center gap-3 cursor-pointer">
                        <input
                          type="radio"
                          name="shipping"
                          value={key}
                          checked={shippingMethod === key}
                          onChange={(e) => setShippingMethod(e.target.value)}
                          className="text-blue-600"
                        />
                        <div className="flex-1">
                          <div className="flex justify-between">
                            <span className="text-sm font-medium">{option.name}</span>
                            <span className="text-sm">
                              {option.price === 0 ? 'Free' : `KES ${option.price}`}
                            </span>
                          </div>
                          <p className="text-xs text-gray-500">{option.days} business days</p>
                        </div>
                      </label>
                    ))}
                  </div>
                </div>
              </Card>

              {/* Order Summary */}
              <Card className="p-4">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Subtotal</span>
                    <span>KES {subtotal.toLocaleString()}</span>
                  </div>
                  {discount > 0 && (
                    <div className="flex justify-between text-green-600">
                      <span>Discount</span>
                      <span>-KES {discount.toLocaleString()}</span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span>Shipping</span>
                    <span>{shipping === 0 ? 'Free' : `KES ${shipping.toLocaleString()}`}</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between font-bold text-lg">
                    <span>Total</span>
                    <span>KES {total.toLocaleString()}</span>
                  </div>
                </div>
              </Card>

              {/* Checkout Button */}
              <Button 
                onClick={handleCheckout} 
                className="w-full flex items-center gap-2"
                size="lg"
              >
                <CreditCard className="h-4 w-4" />
                Proceed to Checkout
                <ArrowRight className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ShoppingCart;
