import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Package,
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Eye,
  ImageIcon,
  Tag,
  DollarSign,
  BarChart3,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  TrendingDown,
  Layers,
  Grid,
  List,
  Loader2,
  RefreshCw
} from 'lucide-react';
import { toast } from 'sonner';
import api from '@/lib/api';

const ProductManagement = () => {
  const [products, setProducts] = useState([]);
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [viewMode, setViewMode] = useState('table');
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const [newProduct, setNewProduct] = useState({
    name: '',
    sku: '',
    description: '',
    price: '',
    costPrice: '',
    category: '',
    subcategory: '',
    stock: '',
    lowStockThreshold: '',
    status: 'active',
    tags: '',
    supplier: ''
  });

  // Sample data for fallback
  const sampleProducts = [
    {
      id: 1,
      name: 'Premium Hair Treatment',
      sku: 'HT-001',
      description: 'Professional hair treatment for damaged hair',
      price: 2500,
      costPrice: 1200,
      category: 'Services',
      subcategory: 'Hair Care',
      stock: 15,
      lowStockThreshold: 5,
      status: 'active',
      images: ['💇'],
      variants: [
        { id: 1, name: 'Standard', price: 2500, stock: 10 },
        { id: 2, name: 'Premium', price: 3500, stock: 5 }
      ],
      tags: ['hair', 'treatment', 'premium'],
      supplier: 'Beauty Supplies Co.',
      createdAt: '2024-01-15',
      updatedAt: '2024-01-15'
    },
    {
      id: 2,
      name: 'Organic Shampoo',
      sku: 'SH-002',
      description: 'Natural organic shampoo for all hair types',
      price: 800,
      costPrice: 400,
      category: 'Products',
      subcategory: 'Hair Products',
      stock: 3,
      lowStockThreshold: 5,
      status: 'active',
      images: ['🧴'],
      variants: [
        { id: 1, name: '250ml', price: 800, stock: 3 },
        { id: 2, name: '500ml', price: 1400, stock: 8 }
      ],
      tags: ['organic', 'shampoo', 'natural'],
      supplier: 'Organic Beauty Ltd.',
      createdAt: '2024-01-14',
      updatedAt: '2024-01-14'
    }
  ];

  const categories = ['Services', 'Products', 'Equipment', 'Accessories'];
  const subcategories = {
    'Services': ['Hair Care', 'Skincare', 'Nail Care', 'Massage'],
    'Products': ['Hair Products', 'Skincare', 'Makeup', 'Tools'],
    'Equipment': ['Styling Tools', 'Treatment Equipment'],
    'Accessories': ['Brushes', 'Towels', 'Capes']
  };

  // API Functions
  const fetchProducts = useCallback(async () => {
    try {
      setLoading(true);
      const response = await api.get('/products');
      const dbProducts = response.data;

      if (dbProducts.length === 0) {
        // Use sample data if no products in database
        setProducts(sampleProducts);
        toast.info('No products found in database. Showing sample data.');
      } else {
        // Transform database products to match frontend format
        const transformedProducts = dbProducts.map(product => ({
          id: product._id,
          name: product.name,
          sku: product.sku || `SKU-${product._id.slice(-6)}`,
          description: product.description || '',
          price: product.price,
          costPrice: product.costPrice || product.price * 0.6,
          category: product.category,
          subcategory: product.subcategory || '',
          stock: product.stockQuantity || 0,
          lowStockThreshold: product.lowStockThreshold || 5,
          status: product.status || 'active',
          images: product.images || [product.image] || ['📦'],
          variants: product.variants || [],
          tags: product.tags || [],
          supplier: product.supplierName || 'Unknown Supplier',
          createdAt: product.createdAt ? new Date(product.createdAt).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
          updatedAt: product.updatedAt ? new Date(product.updatedAt).toISOString().split('T')[0] : new Date().toISOString().split('T')[0]
        }));
        setProducts(transformedProducts);
      }
    } catch (error) {
      console.error('Error fetching products:', error);
      toast.error('Failed to load products. Showing sample data.');
      setProducts(sampleProducts);
    } finally {
      setLoading(false);
    }
  }, []);

  const refreshProducts = useCallback(async () => {
    setRefreshing(true);
    await fetchProducts();
    setRefreshing(false);
    toast.success('Products refreshed successfully!');
  }, [fetchProducts]);

  const createProduct = useCallback(async (productData) => {
    try {
      const response = await api.post('/products', {
        name: productData.name,
        description: productData.description,
        price: parseFloat(productData.price),
        costPrice: parseFloat(productData.costPrice) || parseFloat(productData.price) * 0.6,
        category: productData.category,
        subcategory: productData.subcategory,
        stockQuantity: parseInt(productData.stock) || 0,
        lowStockThreshold: parseInt(productData.lowStockThreshold) || 5,
        sku: productData.sku || `SKU-${Date.now()}`,
        status: productData.status || 'active',
        supplierName: productData.supplier || 'Unknown Supplier',
        tags: productData.tags ? productData.tags.split(',').map(tag => tag.trim()) : [],
        image: productData.image || 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=300&fit=crop'
      });

      await fetchProducts(); // Refresh the list
      toast.success('Product created successfully!');
      return response.data;
    } catch (error) {
      console.error('Error creating product:', error);
      toast.error('Failed to create product. Please try again.');
      throw error;
    }
  }, [fetchProducts]);

  const updateProduct = useCallback(async (productId, productData) => {
    try {
      const response = await api.put(`/products/${productId}`, {
        name: productData.name,
        description: productData.description,
        price: parseFloat(productData.price),
        costPrice: parseFloat(productData.costPrice) || parseFloat(productData.price) * 0.6,
        category: productData.category,
        subcategory: productData.subcategory,
        stockQuantity: parseInt(productData.stock) || 0,
        lowStockThreshold: parseInt(productData.lowStockThreshold) || 5,
        sku: productData.sku,
        status: productData.status,
        supplierName: productData.supplier,
        tags: productData.tags ? productData.tags.split(',').map(tag => tag.trim()) : []
      });

      await fetchProducts(); // Refresh the list
      toast.success('Product updated successfully!');
      return response.data;
    } catch (error) {
      console.error('Error updating product:', error);
      toast.error('Failed to update product. Please try again.');
      throw error;
    }
  }, [fetchProducts]);

  const deleteProduct = useCallback(async (productId) => {
    try {
      await api.delete(`/products/${productId}`);
      await fetchProducts(); // Refresh the list
      toast.success('Product deleted successfully!');
    } catch (error) {
      console.error('Error deleting product:', error);
      toast.error('Failed to delete product. Please try again.');
      throw error;
    }
  }, [fetchProducts]);

  const statusColors = {
    active: 'bg-green-100 text-green-800',
    inactive: 'bg-gray-100 text-gray-800',
    out_of_stock: 'bg-red-100 text-red-800',
    discontinued: 'bg-yellow-100 text-yellow-800'
  };

  // Initial data fetch
  useEffect(() => {
    fetchProducts();
  }, [fetchProducts]);

  // Optimized filtering with useMemo for performance
  const filteredAndSortedProducts = useMemo(() => {
    let filtered = products;

    // Search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchLower) ||
        product.sku.toLowerCase().includes(searchLower) ||
        product.description.toLowerCase().includes(searchLower) ||
        (product.tags && product.tags.some(tag =>
          typeof tag === 'string' && tag.toLowerCase().includes(searchLower)
        ))
      );
    }

    // Category filter
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(product => product.category === categoryFilter);
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(product => product.status === statusFilter);
    }

    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'price':
          return a.price - b.price;
        case 'stock':
          return a.stock - b.stock;
        case 'created':
          return new Date(b.createdAt) - new Date(a.createdAt);
        default:
          return 0;
      }
    });

    return filtered;
  }, [products, searchTerm, categoryFilter, statusFilter, sortBy]);

  // Update filteredProducts when the memoized value changes
  useEffect(() => {
    setFilteredProducts(filteredAndSortedProducts);
  }, [filteredAndSortedProducts]);

  const handleAddProduct = async () => {
    if (!newProduct.name || !newProduct.price || !newProduct.category) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      await createProduct(newProduct);
      setNewProduct({
        name: '', sku: '', description: '', price: '', costPrice: '', category: '',
        subcategory: '', stock: '', lowStockThreshold: '', status: 'active', tags: '', supplier: ''
      });
      setIsAddDialogOpen(false);
    } catch (error) {
      // Error already handled in createProduct
    }
  };

  const handleEditProduct = async () => {
    if (!selectedProduct.name || !selectedProduct.price || !selectedProduct.category) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      await updateProduct(selectedProduct.id, selectedProduct);
      setIsEditDialogOpen(false);
      setSelectedProduct(null);
    } catch (error) {
      // Error already handled in updateProduct
    }
  };

  const handleDeleteProduct = async (productId) => {
    if (window.confirm('Are you sure you want to delete this product?')) {
      try {
        await deleteProduct(productId);
      } catch (error) {
        // Error already handled in deleteProduct
      }
    }
  };

  const getStockStatus = (product) => {
    if (product.stock === 0) return { status: 'Out of Stock', color: 'text-red-600', icon: AlertTriangle };
    if (product.stock <= product.lowStockThreshold) return { status: 'Low Stock', color: 'text-yellow-600', icon: Clock };
    return { status: 'In Stock', color: 'text-green-600', icon: CheckCircle };
  };

  const getTotalValue = () => {
    return products.reduce((sum, product) => sum + (product.price * product.stock), 0);
  };

  const getLowStockCount = () => {
    return products.filter(product => product.stock <= product.lowStockThreshold && product.stock > 0).length;
  };

  const getOutOfStockCount = () => {
    return products.filter(product => product.stock === 0).length;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Product Management</h2>
          <p className="text-gray-600">
            Manage your product catalog and inventory
            {loading && <span className="ml-2 text-blue-600">Loading...</span>}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={refreshProducts}
            disabled={refreshing}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                Add Product
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Add New Product</DialogTitle>
                <DialogDescription>
                  Create a new product in your catalog
                </DialogDescription>
              </DialogHeader>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Product Name *</Label>
                  <Input
                    id="name"
                    value={newProduct.name}
                    onChange={(e) => setNewProduct({...newProduct, name: e.target.value})}
                    placeholder="Enter product name"
                  />
                </div>
                <div>
                  <Label htmlFor="sku">SKU *</Label>
                  <Input
                    id="sku"
                    value={newProduct.sku}
                    onChange={(e) => setNewProduct({...newProduct, sku: e.target.value})}
                    placeholder="Enter SKU"
                  />
                </div>
                <div className="col-span-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={newProduct.description}
                    onChange={(e) => setNewProduct({...newProduct, description: e.target.value})}
                    placeholder="Enter product description"
                    rows={3}
                  />
                </div>
                <div>
                  <Label htmlFor="price">Selling Price *</Label>
                  <Input
                    id="price"
                    type="number"
                    value={newProduct.price}
                    onChange={(e) => setNewProduct({...newProduct, price: e.target.value})}
                    placeholder="0.00"
                  />
                </div>
                <div>
                  <Label htmlFor="costPrice">Cost Price</Label>
                  <Input
                    id="costPrice"
                    type="number"
                    value={newProduct.costPrice}
                    onChange={(e) => setNewProduct({...newProduct, costPrice: e.target.value})}
                    placeholder="0.00"
                  />
                </div>
                <div>
                  <Label htmlFor="category">Category *</Label>
                  <Select value={newProduct.category} onValueChange={(value) => setNewProduct({...newProduct, category: value, subcategory: ''})}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map(category => (
                        <SelectItem key={category} value={category}>{category}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="subcategory">Subcategory</Label>
                  <Select value={newProduct.subcategory} onValueChange={(value) => setNewProduct({...newProduct, subcategory: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select subcategory" />
                    </SelectTrigger>
                    <SelectContent>
                      {newProduct.category && subcategories[newProduct.category]?.map(sub => (
                        <SelectItem key={sub} value={sub}>{sub}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="stock">Stock Quantity *</Label>
                  <Input
                    id="stock"
                    type="number"
                    value={newProduct.stock}
                    onChange={(e) => setNewProduct({...newProduct, stock: e.target.value})}
                    placeholder="0"
                  />
                </div>
                <div>
                  <Label htmlFor="lowStockThreshold">Low Stock Alert</Label>
                  <Input
                    id="lowStockThreshold"
                    type="number"
                    value={newProduct.lowStockThreshold}
                    onChange={(e) => setNewProduct({...newProduct, lowStockThreshold: e.target.value})}
                    placeholder="5"
                  />
                </div>
                <div>
                  <Label htmlFor="supplier">Supplier</Label>
                  <Input
                    id="supplier"
                    value={newProduct.supplier}
                    onChange={(e) => setNewProduct({...newProduct, supplier: e.target.value})}
                    placeholder="Supplier name"
                  />
                </div>
                <div className="col-span-2">
                  <Label htmlFor="tags">Tags (comma separated)</Label>
                  <Input
                    id="tags"
                    value={newProduct.tags}
                    onChange={(e) => setNewProduct({...newProduct, tags: e.target.value})}
                    placeholder="tag1, tag2, tag3"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleAddProduct} disabled={loading}>
                  {loading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Adding...
                    </>
                  ) : (
                    <>
                      <Plus className="h-4 w-4 mr-2" />
                      Add Product
                    </>
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Products</p>
                <p className="text-2xl font-bold">{products.length}</p>
              </div>
              <Package className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Value</p>
                <p className="text-2xl font-bold">KES {getTotalValue().toLocaleString()}</p>
              </div>
              <DollarSign className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Low Stock</p>
                <p className="text-2xl font-bold">{getLowStockCount()}</p>
              </div>
              <Clock className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Out of Stock</p>
                <p className="text-2xl font-bold">{getOutOfStockCount()}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-64">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search products, SKU, or tags..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map(category => (
                  <SelectItem key={category} value={category}>{category}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="All Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="out_of_stock">Out of Stock</SelectItem>
                <SelectItem value="discontinued">Discontinued</SelectItem>
              </SelectContent>
            </Select>
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="name">Name</SelectItem>
                <SelectItem value="price">Price</SelectItem>
                <SelectItem value="stock">Stock</SelectItem>
                <SelectItem value="created">Date Created</SelectItem>
              </SelectContent>
            </Select>
            <div className="flex gap-2">
              <Button
                variant={viewMode === 'table' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('table')}
              >
                <List className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <Grid className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Products Table */}
      <Card>
        <CardHeader>
          <CardTitle>Products ({filteredProducts.length})</CardTitle>
          <CardDescription>
            Manage your product catalog and inventory
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="flex items-center gap-2">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span>Loading products...</span>
              </div>
            </div>
          ) : filteredProducts.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12">
              <Package className="h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No products found</h3>
              <p className="text-gray-500 mb-4">
                {searchTerm || categoryFilter !== 'all' || statusFilter !== 'all'
                  ? 'Try adjusting your search or filter criteria'
                  : 'Get started by adding your first product'
                }
              </p>
              {!searchTerm && categoryFilter === 'all' && statusFilter === 'all' && (
                <Button onClick={() => setIsAddDialogOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Your First Product
                </Button>
              )}
            </div>
          ) : viewMode === 'table' ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>SKU</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Price</TableHead>
                  <TableHead>Stock</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredProducts.map((product) => {
                  const stockStatus = getStockStatus(product);
                  const StockIcon = stockStatus.icon;

                  return (
                    <TableRow key={product.id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <div className="text-2xl">{product.images[0]}</div>
                          <div>
                            <p className="font-medium">{product.name}</p>
                            <p className="text-sm text-gray-500">{product.description}</p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="font-mono text-sm">{product.sku}</TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">{product.category}</p>
                          {product.subcategory && (
                            <p className="text-sm text-gray-500">{product.subcategory}</p>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">KES {product.price.toLocaleString()}</p>
                          {product.costPrice && (
                            <p className="text-sm text-gray-500">Cost: KES {product.costPrice.toLocaleString()}</p>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <StockIcon className={`h-4 w-4 ${stockStatus.color}`} />
                          <div>
                            <p className="font-medium">{product.stock}</p>
                            <p className={`text-xs ${stockStatus.color}`}>{stockStatus.status}</p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={statusColors[product.status]}>
                          {product.status.replace('_', ' ')}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedProduct(product);
                              setIsEditDialogOpen(true);
                            }}
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteProduct(product.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredProducts.map((product) => {
                const stockStatus = getStockStatus(product);
                const StockIcon = stockStatus.icon;

                return (
                  <Card key={product.id} className="hover:shadow-md transition-shadow">
                    <CardContent className="p-4">
                      <div className="space-y-3">
                        <div className="flex items-start justify-between">
                          <div className="text-3xl">{product.images[0]}</div>
                          <Badge className={statusColors[product.status]}>
                            {product.status.replace('_', ' ')}
                          </Badge>
                        </div>

                        <div>
                          <h3 className="font-medium">{product.name}</h3>
                          <p className="text-sm text-gray-500 font-mono">{product.sku}</p>
                          <p className="text-sm text-gray-600 mt-1">{product.description}</p>
                        </div>

                        <div className="flex justify-between items-center">
                          <div>
                            <p className="font-bold text-lg">KES {product.price.toLocaleString()}</p>
                            {product.costPrice && (
                              <p className="text-sm text-gray-500">Cost: KES {product.costPrice.toLocaleString()}</p>
                            )}
                          </div>
                          <div className="text-right">
                            <div className="flex items-center gap-1">
                              <StockIcon className={`h-4 w-4 ${stockStatus.color}`} />
                              <span className="font-medium">{product.stock}</span>
                            </div>
                            <p className={`text-xs ${stockStatus.color}`}>{stockStatus.status}</p>
                          </div>
                        </div>

                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex-1"
                            onClick={() => {
                              setSelectedProduct(product);
                              setIsEditDialogOpen(true);
                            }}
                          >
                            <Edit className="h-3 w-3 mr-1" />
                            Edit
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteProduct(product.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}

          {filteredProducts.length === 0 && (
            <div className="text-center py-8">
              <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No products found</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Product Dialog */}
      {selectedProduct && (
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Edit Product</DialogTitle>
              <DialogDescription>
                Update product information
              </DialogDescription>
            </DialogHeader>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="editName">Product Name *</Label>
                <Input
                  id="editName"
                  value={selectedProduct.name}
                  onChange={(e) => setSelectedProduct({...selectedProduct, name: e.target.value})}
                />
              </div>
              <div>
                <Label htmlFor="editSku">SKU *</Label>
                <Input
                  id="editSku"
                  value={selectedProduct.sku}
                  onChange={(e) => setSelectedProduct({...selectedProduct, sku: e.target.value})}
                />
              </div>
              <div className="col-span-2">
                <Label htmlFor="editDescription">Description</Label>
                <Textarea
                  id="editDescription"
                  value={selectedProduct.description}
                  onChange={(e) => setSelectedProduct({...selectedProduct, description: e.target.value})}
                  rows={3}
                />
              </div>
              <div>
                <Label htmlFor="editPrice">Selling Price *</Label>
                <Input
                  id="editPrice"
                  type="number"
                  value={selectedProduct.price}
                  onChange={(e) => setSelectedProduct({...selectedProduct, price: parseFloat(e.target.value)})}
                />
              </div>
              <div>
                <Label htmlFor="editStock">Stock Quantity *</Label>
                <Input
                  id="editStock"
                  type="number"
                  value={selectedProduct.stock}
                  onChange={(e) => setSelectedProduct({...selectedProduct, stock: parseInt(e.target.value)})}
                />
              </div>
              <div>
                <Label htmlFor="editStatus">Status</Label>
                <Select
                  value={selectedProduct.status}
                  onValueChange={(value) => setSelectedProduct({...selectedProduct, status: value})}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                    <SelectItem value="out_of_stock">Out of Stock</SelectItem>
                    <SelectItem value="discontinued">Discontinued</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="editLowStock">Low Stock Alert</Label>
                <Input
                  id="editLowStock"
                  type="number"
                  value={selectedProduct.lowStockThreshold}
                  onChange={(e) => setSelectedProduct({...selectedProduct, lowStockThreshold: parseInt(e.target.value)})}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleEditProduct}>
                Save Changes
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default ProductManagement;
