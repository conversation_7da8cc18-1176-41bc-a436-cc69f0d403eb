// Performance optimization utilities

// Debounce function for search inputs and API calls
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// Throttle function for scroll events and frequent updates
export const throttle = (func, limit) => {
  let inThrottle;
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

// Memoization utility for expensive calculations
export const memoize = (fn) => {
  const cache = new Map();
  return (...args) => {
    const key = JSON.stringify(args);
    if (cache.has(key)) {
      return cache.get(key);
    }
    const result = fn(...args);
    cache.set(key, result);
    return result;
  };
};

// Image lazy loading utility
export const createImageObserver = (callback) => {
  if (!window.IntersectionObserver) {
    // Fallback for browsers without IntersectionObserver
    return {
      observe: () => {},
      disconnect: () => {}
    };
  }

  return new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        callback(entry.target);
      }
    });
  }, {
    rootMargin: '50px 0px',
    threshold: 0.01
  });
};

// Virtual scrolling utility for large lists
export const calculateVisibleItems = (containerHeight, itemHeight, scrollTop, totalItems) => {
  const visibleCount = Math.ceil(containerHeight / itemHeight);
  const startIndex = Math.floor(scrollTop / itemHeight);
  const endIndex = Math.min(startIndex + visibleCount + 1, totalItems);
  
  return {
    startIndex: Math.max(0, startIndex),
    endIndex,
    visibleCount
  };
};

// Bundle size analyzer
export const analyzeBundleSize = () => {
  if (process.env.NODE_ENV === 'development') {
    const scripts = document.querySelectorAll('script[src]');
    const styles = document.querySelectorAll('link[rel="stylesheet"]');
    
    console.group('Bundle Analysis');
    console.log(`Scripts loaded: ${scripts.length}`);
    console.log(`Stylesheets loaded: ${styles.length}`);
    
    // Estimate bundle size (rough calculation)
    let totalSize = 0;
    scripts.forEach(script => {
      if (script.src.includes('localhost')) {
        totalSize += 100; // Rough estimate in KB
      }
    });
    
    console.log(`Estimated bundle size: ~${totalSize}KB`);
    console.groupEnd();
  }
};

// Performance monitoring
export const performanceMonitor = {
  marks: new Map(),
  
  mark(name) {
    if (performance.mark) {
      performance.mark(name);
      this.marks.set(name, performance.now());
    }
  },
  
  measure(name, startMark, endMark) {
    if (performance.measure) {
      performance.measure(name, startMark, endMark);
      const measure = performance.getEntriesByName(name)[0];
      console.log(`${name}: ${measure.duration.toFixed(2)}ms`);
      return measure.duration;
    }
    return 0;
  },
  
  getDuration(startMark) {
    const startTime = this.marks.get(startMark);
    if (startTime) {
      return performance.now() - startTime;
    }
    return 0;
  },
  
  clear() {
    if (performance.clearMarks) {
      performance.clearMarks();
      performance.clearMeasures();
    }
    this.marks.clear();
  }
};

// Memory usage monitoring
export const memoryMonitor = {
  check() {
    if (performance.memory) {
      const memory = performance.memory;
      return {
        used: Math.round(memory.usedJSHeapSize / 1048576), // MB
        total: Math.round(memory.totalJSHeapSize / 1048576), // MB
        limit: Math.round(memory.jsHeapSizeLimit / 1048576) // MB
      };
    }
    return null;
  },
  
  log() {
    const memory = this.check();
    if (memory) {
      console.log(`Memory: ${memory.used}MB / ${memory.total}MB (limit: ${memory.limit}MB)`);
      if (memory.used / memory.limit > 0.8) {
        console.warn('High memory usage detected!');
      }
    }
  }
};

// Network performance monitoring
export const networkMonitor = {
  measureApiCall: async (apiCall, name = 'API Call') => {
    const startTime = performance.now();
    try {
      const result = await apiCall();
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      console.log(`${name}: ${duration.toFixed(2)}ms`);
      
      // Log slow API calls
      if (duration > 1000) {
        console.warn(`Slow API call detected: ${name} took ${duration.toFixed(2)}ms`);
      }
      
      return result;
    } catch (error) {
      const endTime = performance.now();
      const duration = endTime - startTime;
      console.error(`${name} failed after ${duration.toFixed(2)}ms:`, error);
      throw error;
    }
  }
};

// Component render optimization
export const renderOptimizer = {
  // Check if component should re-render
  shouldUpdate: (prevProps, nextProps, keys = []) => {
    if (keys.length === 0) {
      return JSON.stringify(prevProps) !== JSON.stringify(nextProps);
    }
    
    return keys.some(key => prevProps[key] !== nextProps[key]);
  },
  
  // Create stable callback references
  useStableCallback: (callback, deps) => {
    const ref = useRef(callback);
    const depsRef = useRef(deps);
    
    if (!deps || deps.some((dep, i) => dep !== depsRef.current[i])) {
      ref.current = callback;
      depsRef.current = deps;
    }
    
    return ref.current;
  }
};

// Initialize performance monitoring in development
if (process.env.NODE_ENV === 'development') {
  // Monitor memory usage every 30 seconds
  setInterval(() => {
    memoryMonitor.log();
  }, 30000);
  
  // Analyze bundle size on load
  window.addEventListener('load', () => {
    setTimeout(analyzeBundleSize, 1000);
  });
}

export default {
  debounce,
  throttle,
  memoize,
  createImageObserver,
  calculateVisibleItems,
  performanceMonitor,
  memoryMonitor,
  networkMonitor,
  renderOptimizer
};
